import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 1024 1024'
}
export default defineComponent({
  name: 'WeiboOutlined',
  render: function render(_ctx, _cache) {
    return (
      _openBlock(),
      _createElementBlock(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            _createElementVNode(
              'path',
              {
                d: 'M457.3 543c-68.1-17.7-145 16.2-174.6 76.2c-30.1 61.2-1 129.1 67.8 151.3c71.2 23 155.2-12.2 184.4-78.3c28.7-64.6-7.2-131-77.6-149.2zm-52 156.2c-13.8 22.1-43.5 31.7-65.8 21.6c-22-10-28.5-35.7-14.6-57.2c13.7-21.4 42.3-31 64.4-21.7c22.4 9.5 29.6 35 16 57.3zm45.5-58.5c-5 8.6-16.1 12.7-24.7 9.1c-8.5-3.5-11.2-13.1-6.4-21.5c5-8.4 15.6-12.4 24.1-9.1c8.7 3.2 11.8 12.9 7 21.5zm334.5-197.2c15 4.8 31-3.4 35.9-18.3c11.8-36.6 4.4-78.4-23.2-109a111.39 111.39 0 0 0-106-34.3a28.45 28.45 0 0 0-21.9 33.8a28.39 28.39 0 0 0 33.8 21.8c18.4-3.9 38.3 1.8 51.9 16.7a54.2 54.2 0 0 1 11.3 53.3a28.45 28.45 0 0 0 18.2 36zm99.8-206c-56.7-62.9-140.4-86.9-217.7-70.5a32.98 32.98 0 0 0-25.4 39.3a33.12 33.12 0 0 0 39.3 25.5c55-11.7 114.4 5.4 154.8 50.1c40.3 44.7 51.2 105.7 34 159.1c-5.6 17.4 3.9 36 21.3 41.7c17.4 5.6 36-3.9 41.6-21.2v-.1c24.1-75.4 8.9-161.1-47.9-223.9zM729 499c-12.2-3.6-20.5-6.1-14.1-22.1c13.8-34.7 15.2-64.7.3-86c-28-40.1-104.8-37.9-192.8-1.1c0 0-27.6 12.1-20.6-9.8c13.5-43.5 11.5-79.9-9.6-101c-47.7-47.8-174.6 1.8-283.5 110.6C127.3 471.1 80 557.5 80 632.2C80 775.1 263.2 862 442.5 862c235 0 391.3-136.5 391.3-245c0-65.5-55.2-102.6-104.8-118zM443 810.8c-143 14.1-266.5-50.5-275.8-144.5c-9.3-93.9 99.2-181.5 242.2-195.6c143-14.2 266.5 50.5 275.8 144.4C694.4 709 586 796.6 443 810.8z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
