<template>
  <div class="knowledge-manager-root">
    <!-- 左侧导航栏 -->
    <div class="sidebar">
      <div class="sidebar-title">目录</div>
      <n-space vertical :size="12">
        <n-input v-model:value="pattern" placeholder="搜索" style="border-radius: 8px" />
        <n-switch v-model:value="showIrrelevantNodes">
          <template #checked> 展示搜索无关的节点 </template>
          <template #unchecked> 隐藏搜索无关的节点 </template>
        </n-switch>
        <n-tree
          :show-irrelevant-nodes="showIrrelevantNodes"
          :pattern="pattern"
          :data="treeData"
          block-line
          checkable
          :checked-keys="checkedKeys"
          @update:checked-keys="onCheckedKeysChange"
        />
      </n-space>
    </div>
    <!-- 右侧内容区域 -->
    <div class="main-content">
      <div class="content-scroll">
        <!-- 上部分内容：搜索框居中 -->
        <div class="search-bar-centered">
          <n-input
            v-model="search"
            placeholder="搜索知识点"
            style="border-radius: 8px"
            size="large"
          >
            <template #prefix>
              <n-icon>
                <SearchOutlined />
              </n-icon>
            </template>
          </n-input>
          <n-button size="large" secondary strong style="margin-left: 20px; border-radius: 8px"
            >搜索</n-button
          >
        </div>
        <div class="graph-area">
          <div class="graph-title">知识图谱展示</div>
          <div ref="chartRef" class="graph-container"></div>
        </div>
      </div>
    </div>

    <!-- 悬浮按钮 -->
    <n-float-button :right="50" :bottom="30" type="primary" @click="showDrawer = true">
      <n-badge :value="9" :offset="[6, -8]">
        <n-icon>
          <ShoppingCartOutlined />
        </n-icon>
      </n-badge>
    </n-float-button>

    <!-- 抽屉 -->
    <n-drawer :show="showDrawer" @update:show="showDrawer = $event" :width="400" placement="right">
      <n-drawer-content title="已选知识点" closable>
        <div class="drawer-content">
          <div v-if="selectedKnowledge.length === 0" class="empty-state">
            <n-empty description="暂无选中的知识点" />
          </div>
          <div v-else>
            <n-space vertical :size="12">
              <div class="selected-count">共选中 {{ selectedKnowledge.length }} 个知识点</div>
              <n-scrollbar style="max-height: 400px">
                <n-space vertical :size="8">
                  <n-tag
                    v-for="(item, index) in selectedKnowledge"
                    :key="item"
                    closable
                    @close="removeKnowledge(index)"
                    style="margin-right: 8px; margin-bottom: 8px"
                  >
                    {{ item }}
                  </n-tag>
                </n-space>
              </n-scrollbar>
            </n-space>
          </div>
        </div>
        <template #footer>
          <n-space>
            <n-button @click="clearAll" :disabled="selectedKnowledge.length === 0">
              清空全部
            </n-button>
            <n-button type="primary" :disabled="selectedKnowledge.length === 0">
              批量操作
            </n-button>
          </n-space>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'
import { SearchOutlined, ShoppingCartOutlined } from '@vicons/antd'

const treeData = ref([
  {
    label: '0',
    key: '0',
    children: [
      {
        label: '0-0',
        key: '0-0',
        children: [
          { label: '0-0-0', key: '0-0-0' },
          { label: '0-0-1', key: '0-0-1' },
        ],
      },
      {
        label: '0-1',
        key: '0-1',
        children: [
          { label: '0-1-0', key: '0-1-0' },
          { label: '0-1-1', key: '0-1-1' },
        ],
      },
    ],
  },
  {
    label: '1',
    key: '1',
    children: [
      {
        label: '1-0',
        key: '1-0',
        children: [
          { label: '1-0-0', key: '1-0-0' },
          { label: '1-0-1', key: '1-0-1' },
        ],
      },
      {
        label: '1-1',
        key: '1-1',
        children: [
          { label: '1-1-0', key: '1-1-0' },
          { label: '1-1-1', key: '1-1-1' },
        ],
      },
    ],
  },
])

const pattern = ref('')
const showIrrelevantNodes = ref(false)
const search = ref('')
const checkedKeys = ref<string[]>([])
const selectedKnowledge = ref<string[]>([])
const showDrawer = ref(false)

function onCheckedKeysChange(keys: string[]) {
  checkedKeys.value = keys
  // 展示所有已选知识点的 label
  const allLabels: string[] = []
  function findLabels(nodes: any[]) {
    for (const node of nodes) {
      if (keys.includes(node.key)) {
        allLabels.push(node.label)
      }
      if (node.children) findLabels(node.children)
    }
  }
  findLabels(treeData.value)
  selectedKnowledge.value = allLabels
}

// 移除单个知识点
function removeKnowledge(index: number) {
  const removedLabel = selectedKnowledge.value[index]
  selectedKnowledge.value.splice(index, 1)

  // 同时更新 checkedKeys
  const newCheckedKeys = checkedKeys.value.filter((key) => {
    // 找到对应的节点并检查其 label
    let shouldKeep = true
    function checkNode(nodes: any[]) {
      for (const node of nodes) {
        if (node.key === key && node.label === removedLabel) {
          shouldKeep = false
          return
        }
        if (node.children) checkNode(node.children)
      }
    }
    checkNode(treeData.value)
    return shouldKeep
  })
  checkedKeys.value = newCheckedKeys
}

// 清空所有选中的知识点
function clearAll() {
  selectedKnowledge.value = []
  checkedKeys.value = []
}

const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: ECharts | null = null

const echartsGraphData = {
  nodes: [
    { name: '前端', symbolSize: 60 },
    { name: 'HTML', symbolSize: 40 },
    { name: 'CSS', symbolSize: 40 },
    { name: 'JavaScript', symbolSize: 40 },
    { name: 'Vue', symbolSize: 50 },
    { name: 'React', symbolSize: 50 },
  ],
  links: [
    { source: '前端', target: 'HTML' },
    { source: '前端', target: 'CSS' },
    { source: '前端', target: 'JavaScript' },
    { source: 'JavaScript', target: 'Vue' },
    { source: 'JavaScript', target: 'React' },
  ],
}

const initChart = () => {
  if (!chartRef.value) return
  chartInstance = echarts.init(chartRef.value)
  const options: EChartsOption = {
    tooltip: {},
    series: [
      {
        type: 'graph',
        layout: 'force',
        symbolSize: 50,
        roam: true,
        label: { show: true },
        force: { repulsion: 200, edgeLength: 100 },
        data: echartsGraphData.nodes,
        links: echartsGraphData.links,
        lineStyle: { color: '#aaa' },
      },
    ],
  }
  chartInstance.setOption(options)
}

const resizeChart = () => {
  chartInstance?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', resizeChart)
})

onBeforeUnmount(() => {
  chartInstance?.dispose()
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
.knowledge-manager-root {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}
.sidebar {
  left: 0;
  width: 270px;
  position: sticky;
  height: 100vh;
  border-right: 1px solid #eee;
  background: #fafbfc;
  overflow-y: auto;
  box-sizing: border-box;
  z-index: 20;
  padding-top: 24px;
  padding-left: 24px;
  padding-right: 24px;
}
.main-content {
  flex: 1;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.content-scroll {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}
.search-bar-centered {
  padding: 24px 24px 0 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
.graph-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
}
.graph-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}
.graph-container {
  flex: 1;
  min-height: 500px;
  width: 100%;
}
.sidebar-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 24px;
  letter-spacing: 1px;
}

/* 抽屉相关样式 */
.drawer-content {
  padding: 16px 0;
}
.selected-count {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>

