import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 1024 1024'
}
export default defineComponent({
  name: '<PERSON><PERSON><PERSON>Outlined',
  render: function render(_ctx, _cache) {
    return (
      _openBlock(),
      _createElementBlock(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            _createElementVNode(
              'path',
              {
                d: 'M564.7 230.1V803h60l25.2 71.4L756.3 803h131.5V230.1H564.7zm247.7 497h-59.9l-75.1 50.4l-17.8-50.4h-18V308.3h170.7v418.8zM526.1 486.9H393.3c2.1-44.9 4.3-104.3 6.6-172.9h130.9l-.1-8.1c0-.6-.2-14.7-2.3-29.1c-2.1-15-6.6-34.9-21-34.9H287.8c4.4-20.6 15.7-69.7 29.4-93.8l6.4-11.2l-12.9-.7c-.8 0-19.6-.9-41.4 10.6c-35.7 19-51.7 56.4-58.7 84.4c-18.4 73.1-44.6 123.9-55.7 145.6c-3.3 6.4-5.3 10.2-6.2 12.8c-1.8 4.9-.8 9.8 2.8 13c10.5 9.5 38.2-2.9 38.5-3c.6-.3 1.3-.6 2.2-1c13.9-6.3 55.1-25 69.8-84.5h56.7c.7 32.2 3.1 138.4 2.9 172.9h-141l-2.1 1.5c-23.1 16.9-30.5 63.2-30.8 65.2l-1.4 9.2h167c-12.3 78.3-26.5 113.4-34 127.4c-3.7 7-7.3 14-10.7 20.8c-21.3 42.2-43.4 85.8-126.3 153.6c-3.6 2.8-7 8-4.8 13.7c2.4 6.3 9.3 9.1 24.6 9.1c5.4 0 11.8-.3 19.4-1c49.9-4.4 100.8-18 135.1-87.6c17-35.1 31.7-71.7 43.9-108.9L497 850l5-12c.8-1.9 19-46.3 5.1-95.9l-.5-1.8l-108.1-123l-22 16.6c6.4-26.1 10.6-49.9 12.5-71.1h158.7v-8c0-40.1-18.5-63.9-19.2-64.9l-2.4-3z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
